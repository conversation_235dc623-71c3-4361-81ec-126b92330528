#include "Scheduler.h"

// 全局变量，用于存储任务数量
uint8_t task_num;

// 任务结构体，包含任务函数指针、执行周期（毫秒）和上次执行时间
typedef struct
{
    void (*task_func)(void); // 任务函数指针
    uint32_t rate_ms;        // 任务执行周期（毫秒）
    uint32_t last_run;       // 上次执行时间（毫秒）
} task_t;

// 静态任务数组，存储任务信息，包括任务函数、执行周期和上次执行时间
static task_t scheduler_task[] =
{
    {led_proc, 500, 0}, // 定义任务：led_proc，执行周期为500毫秒，初始上次执行时间为0
    {uart_proc, 100, 0},
};

/**
 * @brief 初始化调度器
 * @details 计算任务数组中的任务数量并存储到全局变量 task_num
 */
void scheduler_init(void)
{
    // 计算任务数量（数组总大小除以单个任务结构体大小）
    task_num = sizeof(scheduler_task) / sizeof(task_t);
}

/**
 * @brief 运行调度器
 * @details 遍历任务数组，检查每个任务是否到达执行时间，若到达则执行任务并更新上次执行时间
 */
void scheduler_run(void)
{
    // 遍历所有任务
    for (uint8_t i = 0; i < task_num; i++)
    {
        // 获取当前系统时间（毫秒）
        uint32_t now_time = get_systicks();

        // 检查当前时间是否达到任务的执行时间（上次执行时间 + 执行周期）
        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
        {
            // 更新任务的上次执行时间为当前时间
            scheduler_task[i].last_run = now_time;

            // 执行任务函数
            scheduler_task[i].task_func();
        }
    }
}
