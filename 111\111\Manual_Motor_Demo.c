/**
 * @file Manual_Motor_Demo.c
 * @brief 纯手动电机控制演示
 * @version 1.0
 * @date 2025-08-01
 * 
 * 演示如何使用纯手动电机控制接口
 * 完全移除PID，直接控制电机
 */

#include "Motor_Control.h"

/**
 * @brief 基本运动演示
 */
void Demo_BasicMovement(void)
{
    // 停止所有电机
    MotorControl_Stop();
    
    // 前进50%速度
    MotorControl_Forward(50.0f);
    
    // 后退30%速度  
    MotorControl_Backward(30.0f);
    
    // 左转40%速度
    MotorControl_TurnLeft(40.0f);
    
    // 右转40%速度
    MotorControl_TurnRight(40.0f);
    
    // 停止
    MotorControl_Stop();
}

/**
 * @brief 精确控制演示
 */
void Demo_PreciseControl(void)
{
    // 直线前进
    MotorControl_SetSpeed(60.0f, 60.0f);
    
    // 直线后退
    MotorControl_SetSpeed(-50.0f, -50.0f);
    
    // 原地左转（左电机反转，右电机正转）
    MotorControl_SetSpeed(-40.0f, 40.0f);
    
    // 原地右转（左电机正转，右电机反转）
    MotorControl_SetSpeed(40.0f, -40.0f);
    
    // 大半径左转
    MotorControl_SetSpeed(20.0f, 70.0f);
    
    // 大半径右转
    MotorControl_SetSpeed(70.0f, 20.0f);
    
    // 停止
    MotorControl_Stop();
}

/**
 * @brief 速度渐变演示
 */
void Demo_SpeedRamp(void)
{
    // 从0逐渐加速到100%
    for(float speed = 0; speed <= 100; speed += 10)
    {
        MotorControl_Forward(speed);
        // 这里应该添加延时函数
        // delay_ms(200);
    }
    
    // 从100%逐渐减速到0
    for(float speed = 100; speed >= 0; speed -= 10)
    {
        MotorControl_Forward(speed);
        // 这里应该添加延时函数
        // delay_ms(200);
    }
    
    MotorControl_Stop();
}

/**
 * @brief 复杂运动模式演示
 */
void Demo_ComplexMovement(void)
{
    // S形运动
    
    // 阶段1：右前方
    MotorControl_SetSpeed(80.0f, 40.0f);
    // delay_ms(1000);
    
    // 阶段2：左前方
    MotorControl_SetSpeed(40.0f, 80.0f);
    // delay_ms(1000);
    
    // 阶段3：直线前进
    MotorControl_SetSpeed(60.0f, 60.0f);
    // delay_ms(1000);
    
    // 停止
    MotorControl_Stop();
}

/**
 * @brief 用户输入控制演示
 * 模拟根据用户输入控制电机
 */
void Demo_UserInput(void)
{
    // 模拟用户输入
    char command = 'w';  // w=前进, s=后退, a=左转, d=右转, x=停止
    float speed = 60.0f;
    
    switch(command)
    {
        case 'w': // 前进
            MotorControl_Forward(speed);
            break;
            
        case 's': // 后退
            MotorControl_Backward(speed);
            break;
            
        case 'a': // 左转
            MotorControl_TurnLeft(speed);
            break;
            
        case 'd': // 右转
            MotorControl_TurnRight(speed);
            break;
            
        case 'x': // 停止
            MotorControl_Stop();
            break;
            
        default:
            MotorControl_Stop();
            break;
    }
}

/**
 * @brief 获取状态演示
 */
void Demo_GetStatus(void)
{
    // 设置一些速度
    MotorControl_SetSpeed(50.0f, -30.0f);
    
    // 获取当前设定的速度
    float left_speed = MotorControl_GetLeftSpeed();   // 应该返回50.0
    float right_speed = MotorControl_GetRightSpeed(); // 应该返回-30.0
    
    // 这里可以将速度值发送到显示器或串口
    // printf("Left: %.1f, Right: %.1f\n", left_speed, right_speed);
}

/**
 * @brief 主演示函数
 * 按顺序执行各种演示
 */
void Motor_Demo_Main(void)
{
    // 基本运动演示
    Demo_BasicMovement();
    
    // 精确控制演示
    Demo_PreciseControl();
    
    // 速度渐变演示
    Demo_SpeedRamp();
    
    // 复杂运动演示
    Demo_ComplexMovement();
    
    // 用户输入演示
    Demo_UserInput();
    
    // 状态获取演示
    Demo_GetStatus();
    
    // 最后停止所有电机
    MotorControl_Stop();
}
