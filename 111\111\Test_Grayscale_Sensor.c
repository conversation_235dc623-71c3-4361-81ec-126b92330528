#include "SysConfig.h"

/* 灰度传感器测试程序
 * 用于验证修复后的灰度传感器数据读取功能
 * 测试内容：
 * 1. 3个地址位的正确切换
 * 2. ADC数据的正确读取
 * 3. 8个通道数据的完整采集
 */

// 测试用的校准数据
unsigned short test_white[8] = {1800, 1800, 1800, 1800, 1800, 1800, 1800, 1800};
unsigned short test_black[8] = {300, 300, 300, 300, 300, 300, 300, 300};

void Test_Grayscale_Sensor(void)
{
    // 初始化传感器结构体
    No_MCU_Sensor sensor;
    unsigned short analog_values[8];
    unsigned short normalized_values[8];
    unsigned char digital_value;
    
    // 系统初始化
    SYSCFG_DL_init();
    
    // 初始化传感器（首次初始化）
    No_MCU_Ganv_Sensor_Init_Frist(&sensor);
    
    // 使用测试校准数据完整初始化传感器
    No_MCU_Ganv_Sensor_Init(&sensor, test_white, test_black);
    
    // 测试循环
    while(1)
    {
        // 执行传感器任务（包含数据采集和处理）
        No_Mcu_Ganv_Sensor_Task_Without_tick(&sensor);
        
        // 获取原始模拟值
        if(Get_Anolog_Value(&sensor, analog_values))
        {
            // 打印原始ADC值（用于调试）
            // 这里可以通过UART或其他方式输出数据
            // printf("ADC Values: %d %d %d %d %d %d %d %d\n", 
            //        analog_values[0], analog_values[1], analog_values[2], analog_values[3],
            //        analog_values[4], analog_values[5], analog_values[6], analog_values[7]);
        }
        
        // 获取归一化值
        if(Get_Normalize_For_User(&sensor, normalized_values))
        {
            // 打印归一化值（用于调试）
            // printf("Normalized: %d %d %d %d %d %d %d %d\n", 
            //        normalized_values[0], normalized_values[1], normalized_values[2], normalized_values[3],
            //        normalized_values[4], normalized_values[5], normalized_values[6], normalized_values[7]);
        }
        
        // 获取数字信号
        digital_value = Get_Digtal_For_User(&sensor);
        
        // 打印数字信号（二进制表示）
        // printf("Digital: 0x%02X (Binary: ", digital_value);
        // for(int i = 7; i >= 0; i--) {
        //     printf("%d", (digital_value >> i) & 1);
        // }
        // printf(")\n");
        
        // 延时（避免过快刷新）
        // delay_ms(100);  // 如果有延时函数的话
        
        // 简单的LED指示（如果有LED的话）
        // 可以根据数字信号控制LED显示传感器状态
        
        // 为了测试，这里添加一个简单的计数器
        static int test_counter = 0;
        test_counter++;
        
        // 每1000次循环检查一次传感器状态
        if(test_counter >= 1000)
        {
            test_counter = 0;
            
            // 检查传感器是否正常工作
            if(sensor.ok)
            {
                // 传感器工作正常
                // 可以在这里添加状态指示
            }
            else
            {
                // 传感器未正常初始化
                // 可以在这里添加错误处理
            }
        }
    }
}

/* 单独测试ADC读取功能 */
void Test_ADC_Reading(void)
{
    unsigned int adc_result;
    
    // 系统初始化
    SYSCFG_DL_init();
    
    while(1)
    {
        // 直接测试ADC读取
        adc_result = adc_getValue();
        
        // 这里可以输出ADC值进行调试
        // printf("Direct ADC Reading: %d\n", adc_result);
        
        // 简单延时
        for(volatile int i = 0; i < 100000; i++);
    }
}

/* 测试地址线切换功能 */
void Test_Address_Switching(void)
{
    // 系统初始化
    SYSCFG_DL_init();
    
    while(1)
    {
        // 测试8个通道的地址线切换
        for(int i = 0; i < 8; i++)
        {
            // 设置地址线（与Get_Analog_value函数中的逻辑相同）
            Switch_Address_0(!(i&0x01));  // 地址线0，对应bit0
            Switch_Address_1(!(i&0x02));  // 地址线1，对应bit1
            Switch_Address_2(!(i&0x04));  // 地址线2，对应bit2
            
            // 读取当前通道的ADC值
            unsigned int adc_value = adc_getValue();
            
            // 这里可以输出每个通道的ADC值
            // printf("Channel %d: ADC = %d\n", i, adc_value);
            
            // 短暂延时
            for(volatile int j = 0; j < 10000; j++);
        }
        
        // 完整循环延时
        for(volatile int i = 0; i < 500000; i++);
    }
}

// 如果要作为主程序运行，取消下面的注释
/*
int main(void)
{
    // 选择要运行的测试
    // Test_ADC_Reading();        // 测试基础ADC读取
    // Test_Address_Switching();  // 测试地址线切换
    Test_Grayscale_Sensor();      // 测试完整的灰度传感器功能
    
    return 0;
}
*/
