/**
 * @file Test_Manual_Control.c
 * @brief 测试纯手动电机控制功能
 * @version 1.0
 * @date 2025-08-01
 * 
 * 用于验证PID完全移除后的手动控制功能
 */

#include "Motor_Control.h"
#include "Motor.h"

/**
 * @brief 测试基本电机控制功能
 */
void Test_BasicControl(void)
{
    // 测试停止功能
    MotorControl_Stop();
    
    // 测试前进
    MotorControl_Forward(50.0f);
    
    // 测试后退
    MotorControl_Backward(30.0f);
    
    // 测试左转
    MotorControl_TurnLeft(40.0f);
    
    // 测试右转
    MotorControl_TurnRight(40.0f);
    
    // 最终停止
    MotorControl_Stop();
}

/**
 * @brief 测试精确控制功能
 */
void Test_PreciseControl(void)
{
    // 测试直接设置速度
    MotorControl_SetSpeed(60.0f, 60.0f);   // 前进
    MotorControl_SetSpeed(-50.0f, -50.0f); // 后退
    MotorControl_SetSpeed(-40.0f, 40.0f);  // 左转
    MotorControl_SetSpeed(40.0f, -40.0f);  // 右转
    
    // 停止
    MotorControl_Stop();
}

/**
 * @brief 测试速度获取功能
 */
void Test_SpeedGet(void)
{
    // 设置不同速度
    MotorControl_SetSpeed(75.0f, -25.0f);
    
    // 获取当前速度
    float left_speed = MotorControl_GetLeftSpeed();
    float right_speed = MotorControl_GetRightSpeed();
    
    // 验证速度值是否正确
    // left_speed 应该是 75.0f
    // right_speed 应该是 -25.0f
    
    MotorControl_Stop();
}

/**
 * @brief 测试底层电机函数
 */
void Test_LowLevelControl(void)
{
    // 测试单个电机控制
    Motor_SetSpeed(&Motor_Left, 50.0f);
    Motor_SetSpeed(&Motor_Right, -30.0f);
    
    // 测试批量控制
    Motor_SetBothSpeed(40.0f, 40.0f);
    
    // 测试停止功能
    Motor_Stop(&Motor_Left);
    Motor_StopAll();
}

/**
 * @brief 测试边界值
 */
void Test_BoundaryValues(void)
{
    // 测试最大值
    MotorControl_SetSpeed(100.0f, 100.0f);
    
    // 测试最小值
    MotorControl_SetSpeed(-100.0f, -100.0f);
    
    // 测试超出范围的值（应该被限制）
    MotorControl_SetSpeed(150.0f, -150.0f);  // 应该被限制为100.0f, -100.0f
    
    // 测试零值
    MotorControl_SetSpeed(0.0f, 0.0f);
}

/**
 * @brief 主测试函数
 */
void Test_ManualControl_Main(void)
{
    // 依次执行各项测试
    Test_BasicControl();
    Test_PreciseControl();
    Test_SpeedGet();
    Test_LowLevelControl();
    Test_BoundaryValues();
    
    // 确保最后停止所有电机
    MotorControl_Stop();
}

/**
 * @brief 验证PID完全移除
 * 这个函数用于确认系统中不再有PID相关代码
 */
void Verify_NoPID(void)
{
    // 这里可以添加一些验证代码
    // 例如检查电机结构体中是否还有PID相关字段
    
    // 验证电机结构体大小（应该比之前小）
    // sizeof(MOTOR_Def_t) 应该不包含PID结构体的大小
    
    // 验证当前速度直接设置
    Motor_SetSpeed(&Motor_Left, 60.0f);
    float current_speed = Motor_Left.Current_Speed;
    // current_speed 应该等于 60.0f
    
    Motor_StopAll();
}
