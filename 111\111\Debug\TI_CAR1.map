******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Aug  1 23:36:05 2025

OUTPUT FILE NAME:   <TI_CAR1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00003dd1


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00005268  0001ad98  R  X
  SRAM                  20200000   00008000  0000040e  00007bf2  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00005268   00005268    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00004760   00004760    r-x .text
  00004820    00004820    000009f0   000009f0    r-- .rodata
  00005210    00005210    00000058   00000058    r-- .cinit
20200000    20200000    00000211   00000000    rw-
  20200000    20200000    000001b5   00000000    rw- .bss
  202001b8    202001b8    00000059   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00004760     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000220            : _printfi.c.obj (.text._pconv_a)
                  00000cb0    00000208     Task_App.o (.text.Task_OLED)
                  00000eb8    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00001094    000001d8     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0000126c    000001b0     Task.o (.text.Task_Start)
                  0000141c    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000015ae    00000002     Task_App.o (.text.Task_IdleFunction)
                  000015b0    0000014c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  000016fc    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00001838    00000134            : qsort.c.obj (.text.qsort)
                  0000196c    00000130     OLED.o (.text.OLED_ShowChar)
                  00001a9c    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00001bbc    00000110     OLED.o (.text.OLED_Init)
                  00001ccc    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001dd8    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00001edc    000000f0     Motor.o (.text.Motor_SetDirc)
                  00001fcc    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000020b0    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  0000218c    000000dc     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  00002268    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00002340    000000b4     Interrupt.o (.text.GROUP1_IRQHandler)
                  000023f4    000000b4     Task.o (.text.Task_Add)
                  000024a8    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  00002552    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002554    000000a8     Motor.o (.text.Motor_SetSpeed)
                  000025fc    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  0000269e    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  000026a0    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002740    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  000027d8    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_Motor_PWM_init)
                  00002864    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  000028e8    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  0000296c    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  000029ee    00000002     --HOLE-- [fill = 0]
                  000029f0    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002a6c    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002ae0    0000006e     OLED.o (.text.OLED_ShowString)
                  00002b4e    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00002bba    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00002c24    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00002c8c    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00002cf2    00000002     --HOLE-- [fill = 0]
                  00002cf4    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00002d58    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00002dbc    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00002e1e    00000002     --HOLE-- [fill = 0]
                  00002e20    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00002e82    00000002     --HOLE-- [fill = 0]
                  00002e84    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00002ee4    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00002f42    00000002     --HOLE-- [fill = 0]
                  00002f44    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00002fa0    0000005c     Task_App.o (.text.Task_Init)
                  00002ffc    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00003058    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  000030b4    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  0000310c    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00003164    00000058            : _printfi.c.obj (.text._pconv_f)
                  000031bc    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00003212    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00003264    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  000032b4    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00003304    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  00003350    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  0000339c    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000033e8    0000004c     OLED.o (.text.OLED_Printf)
                  00003434    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  00003480    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  000034ca    00000002     --HOLE-- [fill = 0]
                  000034cc    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00003516    00000002     --HOLE-- [fill = 0]
                  00003518    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00003560    00000048     ADC.o (.text.adc_getValue)
                  000035a8    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000035ec    00000044     OLED.o (.text.mspm0_i2c_disable)
                  00003630    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00003672    00000002     --HOLE-- [fill = 0]
                  00003674    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  000036b6    00000002     --HOLE-- [fill = 0]
                  000036b8    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  000036f8    00000040     Task_App.o (.text.Task_GraySensor)
                  00003738    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00003778    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000037b8    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000037f8    0000003e     Task.o (.text.Task_CMP)
                  00003836    00000002     --HOLE-- [fill = 0]
                  00003838    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003874    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000038b0    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  000038ec    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00003928    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  00003964    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  000039a0    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  000039dc    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00003a18    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00003a52    00000002     --HOLE-- [fill = 0]
                  00003a54    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00003a8e    00000002     --HOLE-- [fill = 0]
                  00003a90    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003ac4    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003af8    00000030     ADC.o (.text.DL_ADC12_getMemResult)
                  00003b28    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00003b58    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00003b88    0000002c     Interrupt.o (.text.Interrupt_Init)
                  00003bb4    0000002c     Motor.o (.text.Motor_Start)
                  00003be0    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00003c0c    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00003c38    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00003c64    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00003c90    00000028     OLED.o (.text.DL_Common_updateReg)
                  00003cb8    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00003ce0    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00003d08    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00003d30    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00003d58    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00003d80    00000028     SysTick.o (.text.SysTick_Increasment)
                  00003da8    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00003dd0    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003df8    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00003e1e    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00003e44    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00003e68    00000024     Motor.o (.text.Motor_SetBothSpeed)
                  00003e8c    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00003eb0    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00003ed4    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00003ef6    00000002     --HOLE-- [fill = 0]
                  00003ef8    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00003f18    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00003f38    00000020     SysTick.o (.text.Delay)
                  00003f58    00000020     main.o (.text.main)
                  00003f78    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00003f96    00000002     --HOLE-- [fill = 0]
                  00003f98    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00003fb6    00000002     --HOLE-- [fill = 0]
                  00003fb8    0000001c     ADC.o (.text.DL_ADC12_startConversion)
                  00003fd4    0000001c     ADC.o (.text.DL_ADC12_stopConversion)
                  00003ff0    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  0000400c    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00004028    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00004044    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00004060    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  0000407c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00004098    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000040b4    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  000040d0    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  000040ec    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00004108    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00004124    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00004140    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  0000415c    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00004178    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00004194    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  000041ac    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  000041c4    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  000041dc    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  000041f4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  0000420c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00004224    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  0000423c    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00004254    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  0000426c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00004284    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  0000429c    00000018     Motor.o (.text.DL_GPIO_setPins)
                  000042b4    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  000042cc    00000018     OLED.o (.text.DL_GPIO_setPins)
                  000042e4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000042fc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00004314    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  0000432c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00004344    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  0000435c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00004374    00000018     OLED.o (.text.DL_I2C_enablePower)
                  0000438c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  000043a4    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  000043bc    00000018     OLED.o (.text.DL_I2C_reset)
                  000043d4    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  000043ec    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00004404    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  0000441c    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00004434    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  0000444c    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00004464    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  0000447c    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00004494    00000018     Motor.o (.text.DL_Timer_startCounter)
                  000044ac    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  000044c4    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  000044dc    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  000044f4    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  0000450c    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00004524    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  0000453c    00000018     libc.a : vsprintf.c.obj (.text._outs)
                  00004554    00000016     ADC.o (.text.DL_ADC12_disableConversions)
                  0000456a    00000016     ADC.o (.text.DL_ADC12_enableConversions)
                  00004580    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00004596    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  000045ac    00000016     OLED.o (.text.DL_GPIO_readPins)
                  000045c2    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  000045d8    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000045ee    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00004602    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00004616    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  0000462a    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  0000463e    00000002     --HOLE-- [fill = 0]
                  00004640    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00004654    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00004668    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  0000467c    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00004690    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000046a4    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000046b8    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000046cc    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000046de    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000046f0    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00004702    00000010     ADC.o (.text.DL_ADC12_getStatus)
                  00004712    00000002     --HOLE-- [fill = 0]
                  00004714    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00004724    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00004734    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00004744    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00004754    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  00004762    00000002     --HOLE-- [fill = 0]
                  00004764    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00004772    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00004780    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  0000478e    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  0000479a    00000002     --HOLE-- [fill = 0]
                  0000479c    0000000c     SysTick.o (.text.Sys_GetTick)
                  000047a8    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000047b2    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  000047bc    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  000047cc    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  000047d6    0000000a            : vsprintf.c.obj (.text._outc)
                  000047e0    00000008     Interrupt.o (.text.SysTick_Handler)
                  000047e8    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000047f0    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000047f8    00000006     libc.a : exit.c.obj (.text:abort)
                  000047fe    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00004802    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00004806    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  0000480a    00000002     --HOLE-- [fill = 0]
                  0000480c    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  0000481c    00000004            : pre_init.c.obj (.text._system_pre_init)

.cinit     0    00005210    00000058     
                  00005210    0000002d     (.cinit..data.load) [load image, compression = lzss]
                  0000523d    00000003     --HOLE-- [fill = 0]
                  00005240    0000000c     (__TI_handler_table)
                  0000524c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00005254    00000010     (__TI_cinit_table)
                  00005264    00000004     --HOLE-- [fill = 0]

.rodata    0    00004820    000009f0     
                  00004820    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00004e10    0000021c     OLED_Font.o (.rodata.asc2_0806)
                  0000502c    00000003     ti_msp_dl_config.o (.rodata.gMotor_PWMClockConfig)
                  0000502f    00000001     --HOLE-- [fill = 0]
                  00005030    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00005131    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00005133    00000001     --HOLE-- [fill = 0]
                  00005134    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  0000515c    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00005174    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  0000518c    00000016     Task_App.o (.rodata.str1.16020955549137178199.1)
                  000051a2    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  000051b3    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  000051c4    0000000b     Task_App.o (.rodata.str1.10635198597896025474.1)
                  000051cf    0000000b     Task_App.o (.rodata.str1.12629676409056169537.1)
                  000051da    0000000b     Task_App.o (.rodata.str1.8896853068034818020.1)
                  000051e5    00000001     --HOLE-- [fill = 0]
                  000051e6    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  000051f0    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  000051f8    00000008     ti_msp_dl_config.o (.rodata.gMotor_PWMConfig)
                  00005200    00000005     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00005205    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00005207    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  00005209    00000007     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000001b5     UNINITIALIZED
                  20200000    000000f0     Task.o (.bss.Task_Schedule)
                  202000f0    000000b0     (.common:GraySensor)
                  202001a0    00000010     (.common:Gray_Anolog)
                  202001b0    00000004     (.common:ExISR_Flag)
                  202001b4    00000001     (.common:Gray_Digtal)

.data      0    202001b8    00000059     UNINITIALIZED
                  202001b8    00000020     Motor.o (.data.Motor_Left)
                  202001d8    00000020     Motor.o (.data.Motor_Right)
                  202001f8    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202001fc    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200200    00000004     SysTick.o (.data.delayTick)
                  20200204    00000004     Task_App.o (.data.last_time)
                  20200208    00000004     SysTick.o (.data.uwTick)
                  2020020c    00000002     Task_App.o (.data.last_encoder_left)
                  2020020e    00000002     Task_App.o (.data.last_encoder_right)
                  20200210    00000001     Task.o (.data.Task_Num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               3418    123       0      
       startup_mspm0g350x_ticlang.o     8       192       0      
       main.o                           32      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3458    315       0      
                                                                 
    .\APP\Src\
       Task_App.o                       678     60        205    
       Interrupt.o                      378     0         4      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1056    60        209    
                                                                 
    .\BSP\Src\
       OLED_Font.o                      0       2060      0      
       OLED.o                           1854    0         0      
       No_Mcu_Ganv_Grayscale_Sensor.o   1014    0         0      
       Task.o                           674     0         241    
       Motor.o                          556     0         64     
       ADC.o                            236     0         0      
       SysTick.o                        84      0         8      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4418    2060      313    
                                                                 
    C:/ti/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o     388     0         0      
       dl_timer.o                       356     0         0      
       dl_i2c.o                         132     0         0      
       dl_uart.o                        90      0         0      
       dl_dma.o                         76      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1116    0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                   4510    34        0      
       qsort.c.obj                      308     0         0      
       aeabi_ctype.S.obj                0       257       0      
       s_scalbn.c.obj                   216     0         0      
       copy_decompress_lzss.c.obj       124     0         0      
       s_frexp.c.obj                    92      0         0      
       _ltoa.c.obj                      88      0         0      
       vsprintf.c.obj                   78      0         0      
       atoi.c.obj                       64      0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       memccpy.c.obj                    34      0         0      
       copy_zero_init.c.obj             22      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       wcslen.c.obj                     16      0         0      
       memset16.S.obj                   14      0         0      
       aeabi_portable.c.obj             8       0         4      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           5736    291       4      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     418     0         0      
       divdf3.S.obj                     268     0         0      
       muldf3.S.obj                     228     0         0      
       comparedf2.c.obj                 220     0         0      
       udivmoddi4.S.obj                 162     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       aeabi_idivmod.S.obj              86      0         0      
       fixdfsi.S.obj                    74      0         0      
       fixunsdfsi.S.obj                 66      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       extendsfdf2.S.obj                64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsisf.S.obj                40      0         0      
       floatunsidf.S.obj                36      0         0      
       muldi3.S.obj                     36      0         0      
       ashldi3.S.obj                    30      0         0      
       aeabi_uldivmod.S.obj             20      0         0      
       aeabi_memset.S.obj               14      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2444    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       81        0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     18232   2807      1038   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00005254 records: 2, size/record: 8, table size: 16
	.data: load addr=00005210, load size=0000002d bytes, run addr=202001b8, run size=00000059 bytes, compression=lzss
	.bss: load addr=0000524c, load size=00000008 bytes, run addr=20200000, run size=000001b5 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00005240 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   0000141d     000047bc     000047ba   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00003dd1     0000480c     00004806   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000047ff  ADC0_IRQHandler                      
000047ff  ADC1_IRQHandler                      
000047ff  AES_IRQHandler                       
00004802  C$$EXIT                              
000047ff  CANFD0_IRQHandler                    
000047ff  DAC0_IRQHandler                      
000036b9  DL_ADC12_setClockConfig              
000047a9  DL_Common_delayCycles                
00003351  DL_DMA_initChannel                   
00002ee5  DL_I2C_fillControllerTXFIFO          
00003e1f  DL_I2C_setClockConfig                
000020b1  DL_SYSCTL_configSYSPLL               
00002cf5  DL_SYSCTL_setHFCLKSourceHFXTParams   
000035a9  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001dd9  DL_Timer_initFourCCPWMMode           
00004141  DL_Timer_setCaptCompUpdateMethod     
0000447d  DL_Timer_setCaptureCompareOutCtl     
00004725  DL_Timer_setCaptureCompareValue      
0000415d  DL_Timer_setClockConfig              
00003519  DL_UART_init                         
000046cd  DL_UART_setClockConfig               
000047ff  DMA_IRQHandler                       
202001f8  Data_MotorEncoder                    
000047ff  Default_Handler                      
00003f39  Delay                                
202001b0  ExISR_Flag                           
000047ff  GROUP0_IRQHandler                    
00002341  GROUP1_IRQHandler                    
0000218d  Get_Analog_value                     
000038ed  Get_Anolog_Value                     
00004755  Get_Digtal_For_User                  
202000f0  GraySensor                           
202001a0  Gray_Anolog                          
202001b4  Gray_Digtal                          
00004803  HOSTexit                             
000047ff  HardFault_Handler                    
000047ff  I2C0_IRQHandler                      
000047ff  I2C1_IRQHandler                      
00002bbb  I2C_OLED_Clear                       
00003929  I2C_OLED_Set_Pos                     
00002741  I2C_OLED_WR_Byte                     
00002e85  I2C_OLED_i2c_sda_unlock              
00003b89  Interrupt_Init                       
202001b8  Motor_Left                           
202001d8  Motor_Right                          
00003e69  Motor_SetBothSpeed                   
00002555  Motor_SetSpeed                       
00003bb5  Motor_Start                          
000047ff  NMI_Handler                          
000015b1  No_MCU_Ganv_Sensor_Init_Frist        
00003631  No_Mcu_Ganv_Sensor_Task_Without_tick 
00001bbd  OLED_Init                            
000033e9  OLED_Printf                          
0000196d  OLED_ShowChar                        
00002ae1  OLED_ShowString                      
000047ff  PendSV_Handler                       
000047ff  RTC_IRQHandler                       
00004807  Reset_Handler                        
000047ff  SPI0_IRQHandler                      
000047ff  SPI1_IRQHandler                      
000047ff  SVC_Handler                          
00003435  SYSCFG_DL_ADC1_init                  
00003b29  SYSCFG_DL_DMA_CH_RX_init             
00004525  SYSCFG_DL_DMA_CH_TX_init             
0000478f  SYSCFG_DL_DMA_init                   
00001095  SYSCFG_DL_GPIO_init                  
000030b5  SYSCFG_DL_I2C_MPU6050_init           
00002d59  SYSCFG_DL_I2C_OLED_init              
000027d9  SYSCFG_DL_Motor_PWM_init             
00002f45  SYSCFG_DL_SYSCTL_init                
00004735  SYSCFG_DL_SYSTICK_init               
00002865  SYSCFG_DL_UART0_init                 
00003be1  SYSCFG_DL_init                       
000026a1  SYSCFG_DL_initPower                  
000047e1  SysTick_Handler                      
00003d81  SysTick_Increasment                  
0000479d  Sys_GetTick                          
000047ff  TIMA0_IRQHandler                     
000047ff  TIMA1_IRQHandler                     
000047ff  TIMG0_IRQHandler                     
000047ff  TIMG12_IRQHandler                    
000047ff  TIMG6_IRQHandler                     
000047ff  TIMG7_IRQHandler                     
000047ff  TIMG8_IRQHandler                     
000046df  TI_memcpy_small                      
00004781  TI_memset_small                      
000023f5  Task_Add                             
000036f9  Task_GraySensor                      
000015af  Task_IdleFunction                    
00002fa1  Task_Init                            
00000cb1  Task_OLED                            
0000126d  Task_Start                           
000047ff  UART0_IRQHandler                     
000047ff  UART1_IRQHandler                     
000047ff  UART2_IRQHandler                     
000047ff  UART3_IRQHandler                     
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00005254  __TI_CINIT_Base                      
00005264  __TI_CINIT_Limit                     
00005264  __TI_CINIT_Warm                      
00005240  __TI_Handler_Table_Base              
0000524c  __TI_Handler_Table_Limit             
000039dd  __TI_auto_init_nobinit_nopinit       
000029f1  __TI_decompress_lzss                 
000046f1  __TI_decompress_none                 
0000310d  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
000045d9  __TI_zero_init_nomemset              
00001427  __adddf3                             
00005030  __aeabi_ctype_table_                 
00005030  __aeabi_ctype_table_C                
000034cd  __aeabi_d2iz                         
00003675  __aeabi_d2uiz                        
00001427  __aeabi_dadd                         
00002dbd  __aeabi_dcmpeq                       
00002df9  __aeabi_dcmpge                       
00002e0d  __aeabi_dcmpgt                       
00002de5  __aeabi_dcmple                       
00002dd1  __aeabi_dcmplt                       
00001ccd  __aeabi_ddiv                         
00001fcd  __aeabi_dmul                         
0000141d  __aeabi_dsub                         
202001fc  __aeabi_errno                        
000047e9  __aeabi_errno_addr                   
00003779  __aeabi_f2d                          
00002e21  __aeabi_fcmpeq                       
00002e5d  __aeabi_fcmpge                       
00002e71  __aeabi_fcmpgt                       
00002e49  __aeabi_fcmple                       
00002e35  __aeabi_fcmplt                       
0000296d  __aeabi_fdiv                         
00003c39  __aeabi_i2d                          
00003965  __aeabi_i2f                          
000031bd  __aeabi_idiv                         
00002553  __aeabi_idiv0                        
000031bd  __aeabi_idivmod                      
0000269f  __aeabi_ldiv0                        
00003f99  __aeabi_llsl                         
00003eb1  __aeabi_lmul                         
000047f1  __aeabi_memcpy                       
000047f1  __aeabi_memcpy4                      
000047f1  __aeabi_memcpy8                      
00004765  __aeabi_memset                       
00004765  __aeabi_memset4                      
00004765  __aeabi_memset8                      
00003e8d  __aeabi_ui2d                         
00003da9  __aeabi_ui2f                         
00003739  __aeabi_uidiv                        
00003739  __aeabi_uidivmod                     
000046a5  __aeabi_uldivmod                     
00003f99  __ashldi3                            
ffffffff  __binit__                            
00002c25  __cmpdf2                             
00003a19  __cmpsf2                             
00001ccd  __divdf3                             
0000296d  __divsf3                             
00002c25  __eqdf2                              
00003a19  __eqsf2                              
00003779  __extendsfdf2                        
000034cd  __fixdfsi                            
00003675  __fixunsdfsi                         
00003c39  __floatsidf                          
00003965  __floatsisf                          
00003e8d  __floatunsidf                        
00003da9  __floatunsisf                        
00002a6d  __gedf2                              
000039a1  __gesf2                              
00002a6d  __gtdf2                              
000039a1  __gtsf2                              
00002c25  __ledf2                              
00003a19  __lesf2                              
00002c25  __ltdf2                              
00003a19  __ltsf2                              
UNDEFED   __mpu_init                           
00001fcd  __muldf3                             
00003eb1  __muldi3                             
00003a55  __muldsi3                            
00002c25  __nedf2                              
00003a19  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
0000141d  __subdf3                             
000025fd  __udivmoddi4                         
00003dd1  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
0000481d  _system_pre_init                     
000047f9  abort                                
00003561  adc_getValue                         
00004e10  asc2_0806                            
00004820  asc2_1608                            
000037b9  atoi                                 
ffffffff  binit                                
00002b4f  convertAnalogToDigital               
20200200  delayTick                            
00002ffd  frexp                                
00002ffd  frexpl                               
00000000  interruptVectors                     
00002269  ldexp                                
00002269  ldexpl                               
00003f59  main                                 
00003ed5  memccpy                              
000024a9  normalizeAnalogValues                
00001839  qsort                                
00002269  scalbn                               
00002269  scalbnl                              
20200208  uwTick                               
00003c65  vsprintf                             
00004745  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000cb1  Task_OLED                            
00001095  SYSCFG_DL_GPIO_init                  
0000126d  Task_Start                           
0000141d  __aeabi_dsub                         
0000141d  __subdf3                             
00001427  __adddf3                             
00001427  __aeabi_dadd                         
000015af  Task_IdleFunction                    
000015b1  No_MCU_Ganv_Sensor_Init_Frist        
00001839  qsort                                
0000196d  OLED_ShowChar                        
00001bbd  OLED_Init                            
00001ccd  __aeabi_ddiv                         
00001ccd  __divdf3                             
00001dd9  DL_Timer_initFourCCPWMMode           
00001fcd  __aeabi_dmul                         
00001fcd  __muldf3                             
000020b1  DL_SYSCTL_configSYSPLL               
0000218d  Get_Analog_value                     
00002269  ldexp                                
00002269  ldexpl                               
00002269  scalbn                               
00002269  scalbnl                              
00002341  GROUP1_IRQHandler                    
000023f5  Task_Add                             
000024a9  normalizeAnalogValues                
00002553  __aeabi_idiv0                        
00002555  Motor_SetSpeed                       
000025fd  __udivmoddi4                         
0000269f  __aeabi_ldiv0                        
000026a1  SYSCFG_DL_initPower                  
00002741  I2C_OLED_WR_Byte                     
000027d9  SYSCFG_DL_Motor_PWM_init             
00002865  SYSCFG_DL_UART0_init                 
0000296d  __aeabi_fdiv                         
0000296d  __divsf3                             
000029f1  __TI_decompress_lzss                 
00002a6d  __gedf2                              
00002a6d  __gtdf2                              
00002ae1  OLED_ShowString                      
00002b4f  convertAnalogToDigital               
00002bbb  I2C_OLED_Clear                       
00002c25  __cmpdf2                             
00002c25  __eqdf2                              
00002c25  __ledf2                              
00002c25  __ltdf2                              
00002c25  __nedf2                              
00002cf5  DL_SYSCTL_setHFCLKSourceHFXTParams   
00002d59  SYSCFG_DL_I2C_OLED_init              
00002dbd  __aeabi_dcmpeq                       
00002dd1  __aeabi_dcmplt                       
00002de5  __aeabi_dcmple                       
00002df9  __aeabi_dcmpge                       
00002e0d  __aeabi_dcmpgt                       
00002e21  __aeabi_fcmpeq                       
00002e35  __aeabi_fcmplt                       
00002e49  __aeabi_fcmple                       
00002e5d  __aeabi_fcmpge                       
00002e71  __aeabi_fcmpgt                       
00002e85  I2C_OLED_i2c_sda_unlock              
00002ee5  DL_I2C_fillControllerTXFIFO          
00002f45  SYSCFG_DL_SYSCTL_init                
00002fa1  Task_Init                            
00002ffd  frexp                                
00002ffd  frexpl                               
000030b5  SYSCFG_DL_I2C_MPU6050_init           
0000310d  __TI_ltoa                            
000031bd  __aeabi_idiv                         
000031bd  __aeabi_idivmod                      
00003351  DL_DMA_initChannel                   
000033e9  OLED_Printf                          
00003435  SYSCFG_DL_ADC1_init                  
000034cd  __aeabi_d2iz                         
000034cd  __fixdfsi                            
00003519  DL_UART_init                         
00003561  adc_getValue                         
000035a9  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003631  No_Mcu_Ganv_Sensor_Task_Without_tick 
00003675  __aeabi_d2uiz                        
00003675  __fixunsdfsi                         
000036b9  DL_ADC12_setClockConfig              
000036f9  Task_GraySensor                      
00003739  __aeabi_uidiv                        
00003739  __aeabi_uidivmod                     
00003779  __aeabi_f2d                          
00003779  __extendsfdf2                        
000037b9  atoi                                 
000038ed  Get_Anolog_Value                     
00003929  I2C_OLED_Set_Pos                     
00003965  __aeabi_i2f                          
00003965  __floatsisf                          
000039a1  __gesf2                              
000039a1  __gtsf2                              
000039dd  __TI_auto_init_nobinit_nopinit       
00003a19  __cmpsf2                             
00003a19  __eqsf2                              
00003a19  __lesf2                              
00003a19  __ltsf2                              
00003a19  __nesf2                              
00003a55  __muldsi3                            
00003b29  SYSCFG_DL_DMA_CH_RX_init             
00003b89  Interrupt_Init                       
00003bb5  Motor_Start                          
00003be1  SYSCFG_DL_init                       
00003c39  __aeabi_i2d                          
00003c39  __floatsidf                          
00003c65  vsprintf                             
00003d81  SysTick_Increasment                  
00003da9  __aeabi_ui2f                         
00003da9  __floatunsisf                        
00003dd1  _c_int00_noargs                      
00003e1f  DL_I2C_setClockConfig                
00003e69  Motor_SetBothSpeed                   
00003e8d  __aeabi_ui2d                         
00003e8d  __floatunsidf                        
00003eb1  __aeabi_lmul                         
00003eb1  __muldi3                             
00003ed5  memccpy                              
00003f39  Delay                                
00003f59  main                                 
00003f99  __aeabi_llsl                         
00003f99  __ashldi3                            
00004141  DL_Timer_setCaptCompUpdateMethod     
0000415d  DL_Timer_setClockConfig              
0000447d  DL_Timer_setCaptureCompareOutCtl     
00004525  SYSCFG_DL_DMA_CH_TX_init             
000045d9  __TI_zero_init_nomemset              
000046a5  __aeabi_uldivmod                     
000046cd  DL_UART_setClockConfig               
000046df  TI_memcpy_small                      
000046f1  __TI_decompress_none                 
00004725  DL_Timer_setCaptureCompareValue      
00004735  SYSCFG_DL_SYSTICK_init               
00004745  wcslen                               
00004755  Get_Digtal_For_User                  
00004765  __aeabi_memset                       
00004765  __aeabi_memset4                      
00004765  __aeabi_memset8                      
00004781  TI_memset_small                      
0000478f  SYSCFG_DL_DMA_init                   
0000479d  Sys_GetTick                          
000047a9  DL_Common_delayCycles                
000047e1  SysTick_Handler                      
000047e9  __aeabi_errno_addr                   
000047f1  __aeabi_memcpy                       
000047f1  __aeabi_memcpy4                      
000047f1  __aeabi_memcpy8                      
000047f9  abort                                
000047ff  ADC0_IRQHandler                      
000047ff  ADC1_IRQHandler                      
000047ff  AES_IRQHandler                       
000047ff  CANFD0_IRQHandler                    
000047ff  DAC0_IRQHandler                      
000047ff  DMA_IRQHandler                       
000047ff  Default_Handler                      
000047ff  GROUP0_IRQHandler                    
000047ff  HardFault_Handler                    
000047ff  I2C0_IRQHandler                      
000047ff  I2C1_IRQHandler                      
000047ff  NMI_Handler                          
000047ff  PendSV_Handler                       
000047ff  RTC_IRQHandler                       
000047ff  SPI0_IRQHandler                      
000047ff  SPI1_IRQHandler                      
000047ff  SVC_Handler                          
000047ff  TIMA0_IRQHandler                     
000047ff  TIMA1_IRQHandler                     
000047ff  TIMG0_IRQHandler                     
000047ff  TIMG12_IRQHandler                    
000047ff  TIMG6_IRQHandler                     
000047ff  TIMG7_IRQHandler                     
000047ff  TIMG8_IRQHandler                     
000047ff  UART0_IRQHandler                     
000047ff  UART1_IRQHandler                     
000047ff  UART2_IRQHandler                     
000047ff  UART3_IRQHandler                     
00004802  C$$EXIT                              
00004803  HOSTexit                             
00004807  Reset_Handler                        
0000481d  _system_pre_init                     
00004820  asc2_1608                            
00004e10  asc2_0806                            
00005030  __aeabi_ctype_table_                 
00005030  __aeabi_ctype_table_C                
00005240  __TI_Handler_Table_Base              
0000524c  __TI_Handler_Table_Limit             
00005254  __TI_CINIT_Base                      
00005264  __TI_CINIT_Limit                     
00005264  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202000f0  GraySensor                           
202001a0  Gray_Anolog                          
202001b0  ExISR_Flag                           
202001b4  Gray_Digtal                          
202001b8  Motor_Left                           
202001d8  Motor_Right                          
202001f8  Data_MotorEncoder                    
202001fc  __aeabi_errno                        
20200200  delayTick                            
20200208  uwTick                               
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[225 symbols]
