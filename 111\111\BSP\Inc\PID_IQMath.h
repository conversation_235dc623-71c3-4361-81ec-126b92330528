#ifndef __PID_IQMath_h
#define __PID_IQMath_h

#include <stdint.h>
#include "ti/iqmath/include/IQmathLib.h"

typedef struct
{
    _iq Kp; //比例系数
    _iq Ki; //积分系数
    _iq Kd; //微分系数
    _iq Acutal_Now; //当前实际值
    _iq Acutal_Last; //上一次实际值
    _iq Target; //目标值
    _iq Out; //输出值
    _iq Dif_Out; //微分项输出值
    _iq Err_Now; //当前误差
    _iq Err_Last; //上次误差
    _iq Err_Int; //误差累积
} PID_IQ_Def_t;

void PID_IQ_Init(PID_IQ_Def_t *pid);
void PID_IQ_Prosc(PID_IQ_Def_t *pid);
void PID_IQ_SetParams(PID_IQ_Def_t *pid, float kp, float ki, float kd);

#endif
