# 电机控制测试项目

## 项目简介
这是一个简化的电机控制项目，专门用于测试双电机的基本转动功能。项目已经移除了所有非电机控制相关的模块，只保留核心的电机控制功能。

## 功能特性
- ✅ 双电机PWM控制
- ✅ 电机编码器反馈
- ✅ PID速度控制
- ✅ 自动测试模式（前进、后退、左转、右转、停止）
- ✅ 任务调度系统
- ✅ 系统时钟管理

## 已删除的模块
- ❌ OLED显示模块
- ❌ MPU6050陀螺仪模块
- ❌ 循迹传感器模块
- ❌ 串口通信模块
- ❌ ADC模块
- ❌ 按键LED模块
- ❌ DMP库

## 项目结构
```
007/
├── main.c                           # 主程序入口
├── empty.syscfg                     # 系统配置文件
├── APP/
│   ├── Inc/
│   │   ├── SysConfig.h             # 系统配置头文件
│   │   ├── Task_App.h              # 应用任务头文件
│   │   └── Interrupt.h             # 中断处理头文件
│   └── Src/
│       ├── Task_App.c              # 应用任务实现
│       └── Interrupt.c             # 中断处理实现
├── BSP/
│   ├── Inc/
│   │   ├── Motor.h                 # 电机控制头文件
│   │   ├── PID_IQMath.h           # PID控制头文件
│   │   ├── Task.h                  # 任务调度头文件
│   │   └── SysTick.h              # 系统时钟头文件
│   └── Src/
│       ├── Motor.c                 # 电机控制实现
│       ├── PID_IQMath.c           # PID控制实现
│       ├── Task.c                  # 任务调度实现
│       └── SysTick.c              # 系统时钟实现
└── Debug/
    ├── ti_msp_dl_config.h         # 硬件配置头文件
    └── ti_msp_dl_config.c         # 硬件配置实现
```

## 电机测试模式
项目包含自动测试功能，每2秒切换一次电机状态：
1. **停止** - 两个电机都停止
2. **前进** - 两个电机同向转动
3. **后退** - 两个电机反向转动
4. **左转** - 右电机转速高于左电机
5. **右转** - 左电机转速高于右电机

## 编译和运行
1. 使用TI Code Composer Studio打开项目
2. 编译项目
3. 下载到MSPM0G3507开发板
4. 电机将自动开始测试循环

## 硬件连接
- 左电机：PWM通道0，编码器A相连接到指定GPIO
- 右电机：PWM通道1，编码器A相连接到指定GPIO
- TB6612电机驱动器：AIN1/AIN2控制左电机，BIN1/BIN2控制右电机

## 注意事项
- 确保电机驱动器正确连接
- 检查编码器信号线连接
- 调整PID参数以获得最佳控制效果
- 可以修改`Data_Motor_TarSpeed`来调整电机速度
