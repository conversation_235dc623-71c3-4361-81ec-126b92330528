################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Add inputs and outputs from these tool invocations to the build variables 
SYSCFG_SRCS += \
../empty.syscfg 

C_SRCS += \
../Manual_Motor_Demo.c \
../Motor_Control_Example.c \
../Test_Grayscale_Sensor.c \
../Test_Manual_Control.c \
./ti_msp_dl_config.c \
C:/ti/mspm0_sdk_2_04_00_06/source/ti/devices/msp/m0p/startup_system_files/ticlang/startup_mspm0g350x_ticlang.c \
../main.c 

GEN_CMDS += \
./device_linker.cmd 

GEN_FILES += \
./device_linker.cmd \
./device.opt \
./ti_msp_dl_config.c 

C_DEPS += \
./Manual_Motor_Demo.d \
./Motor_Control_Example.d \
./Test_Grayscale_Sensor.d \
./Test_Manual_Control.d \
./ti_msp_dl_config.d \
./startup_mspm0g350x_ticlang.d \
./main.d 

GEN_OPTS += \
./device.opt 

OBJS += \
./Manual_Motor_Demo.o \
./Motor_Control_Example.o \
./Test_Grayscale_Sensor.o \
./Test_Manual_Control.o \
./ti_msp_dl_config.o \
./startup_mspm0g350x_ticlang.o \
./main.o 

GEN_MISC_FILES += \
./device.cmd.genlibs \
./ti_msp_dl_config.h \
./Event.dot 

OBJS__QUOTED += \
"Manual_Motor_Demo.o" \
"Motor_Control_Example.o" \
"Test_Grayscale_Sensor.o" \
"Test_Manual_Control.o" \
"ti_msp_dl_config.o" \
"startup_mspm0g350x_ticlang.o" \
"main.o" 

GEN_MISC_FILES__QUOTED += \
"device.cmd.genlibs" \
"ti_msp_dl_config.h" \
"Event.dot" 

C_DEPS__QUOTED += \
"Manual_Motor_Demo.d" \
"Motor_Control_Example.d" \
"Test_Grayscale_Sensor.d" \
"Test_Manual_Control.d" \
"ti_msp_dl_config.d" \
"startup_mspm0g350x_ticlang.d" \
"main.d" 

GEN_FILES__QUOTED += \
"device_linker.cmd" \
"device.opt" \
"ti_msp_dl_config.c" 

C_SRCS__QUOTED += \
"../Manual_Motor_Demo.c" \
"../Motor_Control_Example.c" \
"../Test_Grayscale_Sensor.c" \
"../Test_Manual_Control.c" \
"./ti_msp_dl_config.c" \
"C:/ti/mspm0_sdk_2_04_00_06/source/ti/devices/msp/m0p/startup_system_files/ticlang/startup_mspm0g350x_ticlang.c" \
"../main.c" 

SYSCFG_SRCS__QUOTED += \
"../empty.syscfg" 


