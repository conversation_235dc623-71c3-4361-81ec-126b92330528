#ifndef _KEY_H
#define _KEY_H
#include "ti_msp_dl_config.h"
#include "bsp_system.h"

#define KEY  DL_GPIO_readPins(KEY_PORT,KEY_key_PIN)
// uint8_t click_N_Double (uint8_t time);  //��������ɨ����˫������ɨ��
// uint8_t click(void);               //��������ɨ��
// uint8_t Long_Press(void);           //����ɨ��
void Key(void);
void Key_1(void);
// extern volatile int Flag_stop;
//extern volatile int Run;
// extern volatile int Flag_stop1;

#endif
